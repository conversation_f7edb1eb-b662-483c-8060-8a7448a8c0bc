

.patient-cards {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.patient-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.action-buttons {
    display: flex;
    justify-content: space-between;
    gap: 6px;
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #e9ecef;
}

.action-btn {
    min-width: 40px;
    min-height: 40px;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: scale(1.05);
}

.menu-btn {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.menu-btn:hover {
    background: #6c757d;
    color: white;
}

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.text-lg {
    font-size: 1rem;
}

.font-medium {
    font-weight: 500;
}

.h-6 {
    height: 1.5rem !important;
}

.border-l-2 {
    border-left: 2px solid #ddd !important;
}

.h-1 {
    height: 0.25rem !important;
}

.w-12 {
    width: 3rem !important;
}

.border-t-2 {
    border-top: 2px solid #ddd !important;
}

.gap-x-4 {
    column-gap: 1rem !important;
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
}

.grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr)) !important;
}

.grid {
    display: grid !important;
}

.items-center {
    align-items: center !important;
}

.h-4 {
    height: 1rem !important;
}

.mt-auto {
    margin-top: auto !important;
}

.bg-gray-50 {
    background-color: #F8FAFC !important;
}

.bg-white {
    background: white !important;
}

.border {
    border: 1px solid #ddd !important;
}

.rounded-md {
    border-radius: 0.375rem !important;
}

.px-8 {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
}

.ml-2 {
    margin-left: 0.5rem !important;
}

/* ############# New Encounter CSS start ############## */

.details-header {
    background-color: #fff;
    padding: 16px;
    display: flex;
    align-items: center;

    .back-btn {
        background: none;
        border: none;
        cursor: pointer;
        margin-right: 1rem;
    }

    .patient-name {
        font-size: 1rem;
        font-weight: 500;
    }
}

.mark-seen-btn {
    background-color: #fff;
    border: 1px solid #007bff;
    color: #007bff;
    padding: 12px;
    border-radius: 2px;
    width: 100%;
}

.details-buttons {
    display: flex;
    gap: 16px;

    .details-btn {
        flex: 1;
        padding: 12px;
        border: 1px solid #ddd;
        background-color: #fff;
        border-radius: 2px;
    }

}

.code-content {
    background-color: #FFF;
    margin: 10px 0;
    padding: 1.5rem;

    .code-section {
        margin-bottom: 1rem;
        background-color: #fff;
        overflow: hidden;

        .code-item {
            display: flex;
            align-items: center;
            border: 1px solid #ddd;
            border-radius: 6px;
            cursor: pointer;

            .code-icon {
                padding: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 12px;
            }

            .code-desc {
                display: flex;
                padding: 16px;
                width: 100%;
                align-items: center;
            }

            .cpt-icon {
                background-color: #DAECFF;
            }

            .icd-icon {
                background-color: #FFDFE2;
            }

            .chevron {
                margin-left: auto;
                color: #ccc;
            }
        }
    }
}