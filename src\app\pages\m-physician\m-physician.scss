@import url('https://fonts.googleapis.com/css2?family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap');

.patient-list-container {
    padding: 8px;
    background-color: #f5f5f5;
    min-height: 100vh;
    font-family: 'Ubuntu', sans-serif;
    color: #1E293B;
    font-weight: 400;
}

.header-section {
    background: white;
    border-radius: 8px;
    padding: 14px;
    margin-bottom: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-container {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.search-input-wrapper {
    flex: 1;
    position: relative;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.search-input {
    width: 100%;
    padding: 12px 12px 12px 36px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: #f8f9fa;
}

.sort-btn,
.filter-btn {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    cursor: pointer;
}

.sort-btn:hover,
.filter-btn:hover {
    background-color: #f8f9fa;
}

.filter-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.facility-dropdown {
    flex: 1;
    margin-right: 8px;
}

.form-select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

.tabs {
    display: flex;
    gap: 4px;
    align-items: center;
    padding: 0.25rem;
    background-color: #E2E8F0;
    border-radius: 0.25rem;
}

.tab-btn {
    padding: 6px 12px;
    border: none;
    background: transparent;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
}

.tab-btn.active {
    background: white;
}

.tab-btn:hover:not(.active) {
    background: #e9ecef;
}
