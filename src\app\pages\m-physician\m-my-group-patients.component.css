
/* Group Patients Page - Mobile-first responsive design */

/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.4;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
    max-width: 100%;
    margin: 0 auto;
    background-color: #ffffff;
    min-height: 100vh;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.title {
    font-size: 18px;
    font-weight: 600;
    color: #d32f2f; /* Red color matching "My Group Patients" */
    margin: 0;
}

.patient-count {
    background-color: #f5f5f5;
    color: #666;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
}

.menu-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    padding: 8px;
    cursor: pointer;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Search Section */
.search-section {
    padding: 16px 20px;
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.search-input {
    flex: 1;
    padding: 12px 16px 12px 40px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    background-color: #fafafa;
    color: #333;
}

.search-input::placeholder {
    color: #999;
}

.search-icon {
    position: absolute;
    left: 12px;
    color: #999;
    font-size: 14px;
    z-index: 1;
}

.filter-btn, .sort-btn {
    background: none;
    border: none;
    padding: 12px;
    margin-left: 8px;
    color: #666;
    cursor: pointer;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filter-row {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
}

.physician-row {
    display: flex;
}

.facility-select, .group-select, .physician-select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background-color: #ffffff;
    font-size: 14px;
    color: #333;
    min-height: 40px;
}

.physician-select {
    width: 100%;
}

/* Patient List */
.patient-list {
    padding: 0 20px 20px;
}

/* Patient Card */
.patient-card {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 16px;
    padding: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.patient-header {
    margin-bottom: 12px;
}

.patient-name {
    font-size: 16px;
    font-weight: 600;
    color: #2e7d32; /* Green color matching patient names */
    margin-bottom: 4px;
}

.patient-details {
    display: flex;
    gap: 12px;
    font-size: 14px;
    color: #666;
}

.patient-info {
    margin-bottom: 12px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    font-size: 14px;
}

.account, .status {
    color: #333;
    font-weight: 500;
}

.room, .los {
    color: #666;
}

.insurance-info, .provider-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
}

.insurance-icon, .provider-icon {
    font-size: 12px;
    color: #999;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
}

.action-btn {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    border-radius: 6px;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.action-btn:hover {
    background-color: #f5f5f5;
}

.delete-btn {
    color: #d32f2f; /* Red for delete */
}

.edit-btn {
    color: #666; /* Gray for edit */
}

.profile-btn {
    color: #4a90e2; /* Blue for profile */
}

.play-btn {
    color: #2e7d32; /* Green for play */
}

.more-btn {
    color: #666; /* Gray for more options */
}

/* Responsive Design - Tablet and Desktop */
@media (min-width: 768px) {
    .container {
        max-width: 768px;
        margin: 20px auto;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    .search-container {
        max-width: 500px;
    }
    
    .filter-row {
        max-width: 500px;
    }
    
    .physician-row {
        max-width: 240px;
    }
    
    .patient-card {
        padding: 20px;
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 900px;
    }
    
    .patient-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 20px;
    }
}
